use crate::ck::ck_sink::SinkHandler;
use crate::dto::RowWrapper;
use arrow::array::RecordBatch;
use ck_provider::*;
use clickhouse::Row;
use parquet_provider::hdfs_provider::{HdfsConfig, HdfsProvider};
use parquet_provider::parquet_provider::{write_parquet_multi, ParquetProviderError, TempDirCleanup};
use parquet_provider::{RecordBatchStreamWriter, RecordBatchWrapper};
use rayon::prelude::*;
use serde::Serialize;
use std::error::Error;
use std::fs;
use std::sync::Arc;
use std::time::Duration;
use tokio::task::JoinHandle;
use uuid::Uuid;

pub struct WriteHandle<T>
where
    T: Sync + Send + Serialize + Row + 'static,
{
    ck_sender: AsyncCkSender<T>,
    parquet_sender: AsyncCkSender<RecordBatch>,
    ck_handle: Jo<PERSON><PERSON><PERSON><PERSON><()>,
    parquet_processor: GenericStreamProcessor<RecordBatch, RecordBatchStreamWriter>,
    file_path: String,
    temp_file_path: String,
    hdfs_config: Option<HdfsConfig>,
    temp_dir_cleanup: TempDirCleanup,
}

impl<T> WriteHandle<T>
where
    T: Sync + Send + Serialize + Row + 'static,
{
    pub async fn new(
        sink_handler: &(impl SinkHandler + Send + Sync),
        ck_config: CkConfig,
        file_path: &str,
        hdfs_config: Option<&HdfsConfig>,
        max_concurrent_flushes: usize,
        batch_size: usize,
    ) -> Result<WriteHandle<T>, Box<dyn Error + Send + Sync>> {
        let db_table_name = format!("{}.{}", sink_handler.db_name(), sink_handler.table_name());

        // 如果有HDFS配置，先删除目标文件
        if let Some(hdfs_config) = hdfs_config {
            let hdfs_provider = HdfsProvider::new(hdfs_config.clone())?;
            hdfs_provider.delete(file_path).await?;
            log::info!("Successfully deleted HDFS file: {}", file_path);
        }

        let optimal_buffer_size = std::cmp::max(batch_size * 4, 2000);

        let ck_stream_config = StreamConfig::default()
            .with_buffer_size(optimal_buffer_size)
            .with_batch_size(batch_size)
            .with_flush_interval(Duration::from_millis(500))
            .with_max_retries(3)
            .with_backpressure_timeout(Duration::from_secs(600))
            .with_parallel_flush(true)
            .with_max_concurrent_flushes(max_concurrent_flushes);

        // Parquet流配置 - 串行写入优化
        let parquet_stream_config = StreamConfig::default()
            .with_buffer_size(5)
            .with_batch_size(1)
            .with_flush_interval(Duration::from_millis(100))
            .with_max_retries(0)
            .with_parallel_flush(false)
            .with_max_concurrent_flushes(1);

        // 创建两个独立的流式通道
        let (ck_sender, ck_receiver) = AsyncCkChannel::new::<T>(ck_stream_config.clone(), 10);
        let (parquet_sender, parquet_receiver) = AsyncCkChannel::new::<RecordBatch>(parquet_stream_config.clone(), 2);

        // 创建ClickHouse流处理器
        let ck_provider = CkProviderImpl::new(ck_config.clone());
        let ck_processor = CkStreamProcessorBuilder::new()
            .with_receiver(ck_receiver)
            .with_provider(ck_provider.clone())
            .with_config(ck_stream_config)
            .with_table_name(db_table_name)
            .build()?;

        // 准备Parquet临时文件
        let temp_uuid = Uuid::new_v4().to_string();
        let base_path = hdfs_config.map(|c| c.local_path_prefix.as_str()).unwrap_or("/tmp");
        let temp_dir = format!("{}/deploy/onedata/dataware/dataware-dw-test-item/data/write/{}", base_path, temp_uuid);
        log::info!("write parquet to tmp dir: {}", temp_dir);

        let temp_dir_cleanup = TempDirCleanup::new(&temp_dir);
        fs::create_dir_all(&temp_dir).map_err(|e| {
            ParquetProviderError::FileOperationError(format!("Failed to create temp directory {}: {}", temp_dir, e))
        })?;

        let file_name = "all.parquet";
        let temp_file_path = format!("{}/{}", temp_dir, file_name);

        // 创建Parquet流处理器
        let parquet_writer = RecordBatchStreamWriter::new(temp_file_path.clone())?;
        let parquet_processor = GenericStreamProcessorBuilder::new()
            .with_receiver(parquet_receiver)
            .with_writer(parquet_writer)
            .with_config(parquet_stream_config)
            .build()
            .map_err(|e| {
                ParquetProviderError::RuntimeError(format!("Failed to build parquet stream processor: {}", e))
            })?;

        // 启动ClickHouse流处理器
        let ck_processor_handle: JoinHandle<()> = tokio::spawn(async move {
            if let Err(e) = ck_processor.start().await {
                eprintln!("ClickHouse流处理器错误: {:?}", e);
            }
        });

        Ok(WriteHandle {
            ck_sender,
            parquet_sender,
            ck_handle: ck_processor_handle,
            parquet_processor,
            file_path: file_path.to_string(),
            temp_file_path,
            hdfs_config: hdfs_config.map(|c| c.clone()),
            temp_dir_cleanup,
        })
    }

    pub async fn apply(
        self,
        task: JoinHandle<Result<(), Box<dyn Error + Send + Sync>>>,
    ) -> Result<(), CkProviderError> {
        let send_handle = tokio::spawn(async move {
            // 等待所有生产者任务完成

            if let Err(e) = task.await {
                log::error!("Task join error: {}", e);
            }

            // 发送结束信号到两个流
            if let Err(e) = self.ck_sender.send(None).await {
                log::error!("Failed to send CK end signal: {}", e)
            };

            if let Err(e) = self.parquet_sender.send(None).await {
                log::error!("Failed to send Parquet end signal: {}", e)
            };
        });
        self.parquet_processor.start().await?;

        send_handle
            .await
            .map_err(|e| ParquetProviderError::RuntimeError(format!("Send handle error: {}", e)))?;

        self.ck_handle.await.map_err(|e| CkProviderError::RuntimeError(e.to_string()))?;
        log::info!("Successfully completed concurrent writing to ClickHouse and Parquet temp file: {}", self.temp_file_path);

        // 如果有HDFS配置，上传文件
        if let Some(hdfs_config) = self.hdfs_config {
            let hdfs_provider =
                HdfsProvider::new(hdfs_config.clone()).map_err(|e| CkProviderError::RuntimeError(e.to_string()))?;
            hdfs_provider
                .upload(&self.temp_file_path, self.file_path.as_str())
                .await
                .map_err(|e| CkProviderError::RuntimeError(e.to_string()))?;
            log::info!("Successfully uploaded parquet file to HDFS: {}", self.file_path);
        }
        drop(self.temp_dir_cleanup);
        Ok(())
    }
}

pub async fn write_data_to_clickhouse_and_parquet<
    T: RowWrapper<P> + Row + Sync + Send + Serialize + 'static,
    P: RecordBatchWrapper + Sync + Send + 'static,
>(
    data: Vec<P>,
    sink_handler: &(impl SinkHandler + Send + Sync),
    hdfs_path: &String,
    ck_config: CkConfig,
    hdfs_config: Option<&HdfsConfig>,
    max_concurrent_flushes: usize,
    batch_size: usize,
) -> Result<Vec<P>, Box<dyn Error + Send + Sync>> {
    let write_handle =
        WriteHandle::new(sink_handler, ck_config, &hdfs_path, hdfs_config, max_concurrent_flushes, batch_size).await?;

    log::info!("开始写入数据到ClickHouse和Parquet，数据量: {}", data.len());
    let ck_sender = write_handle.ck_sender.clone();
    let parquet_sender = write_handle.parquet_sender.clone();

    let data_arc: Arc<Vec<P>> = Arc::new(data);
    let data_arc_clone = data_arc.clone();
    let task: JoinHandle<Result<(), Box<dyn Error + Send + Sync>>> = tokio::spawn(async move {
        let batch_start = std::time::Instant::now();
        let mut ck_build_duration_total = Duration::new(0, 0);
        let mut ck_send_duration_total = Duration::new(0, 0);
        let mut parquet_build_duration_total = Duration::new(0, 0);
        let mut parquet_send_duration_total = Duration::new(0, 0);

        for chunk in data_arc_clone.clone().chunks(batch_size) {
            // 分块处理数据
            for small_chunk in chunk.chunks(10000) {
                // 构建ClickHouse数据
                let ck_build_start = std::time::Instant::now();
                let rows = small_chunk.par_iter().map(|item| T::from_hdfs_entity(item)).collect::<Vec<_>>();
                let ck_build_duration = ck_build_start.elapsed();
                ck_build_duration_total += ck_build_duration;

                // 发送到ClickHouse流
                let ck_send_start = std::time::Instant::now();
                ck_sender.send_batch(Some(rows)).await?;
                let ck_send_duration = ck_send_start.elapsed();
                ck_send_duration_total += ck_send_duration;
            }

            let parquet_build_start = std::time::Instant::now();
            let record_batch = P::to_record_batch(&chunk)?;
            let parquet_build_duration = parquet_build_start.elapsed();
            parquet_build_duration_total += parquet_build_duration;

            // 发送到Parquet流
            let parquet_send_start = std::time::Instant::now();
            parquet_sender.send_batch(Some(vec![record_batch])).await?;
            let parquet_send_duration = parquet_send_start.elapsed();
            parquet_send_duration_total += parquet_send_duration;
        }

        let total_duration = batch_start.elapsed();
        log::info!(
            "完成双重写入 - CK构建: {:?}, CK发送: {:?}, Parquet构建: {:?}, Parquet发送: {:?}, 总耗时: {:?}",
            ck_build_duration_total,
            ck_send_duration_total,
            parquet_build_duration_total,
            parquet_send_duration_total,
            total_duration
        );
        Ok(())
    });
    write_handle.apply(task).await?;

    let data = Arc::try_unwrap(data_arc).map_err(|_| "Failed to unwrap Arc<Vec<P>>")?;
    Ok(data)
}


pub async fn write_to_hdfs<T>(
    table_path: &str,
    data: &[T],
    batch_size: usize,
    hdfs_config: &HdfsConfig,
) -> Result<(), Box<dyn Error + Send + Sync>>
where
    T: RecordBatchWrapper + Send + Sync + Clone + 'static,
{
    if data.is_empty() {
        log::warn!("No data to write to HDFS");
        return Ok(());
    }

    log::info!("写入parquet文件到路径: {}", table_path);

    // 将数据包装成Vec<Vec<T>>格式以匹配write_parquet_multi的期望
    let data_vec = data.to_vec();
    let data_batches = vec![data_vec];
    let data_refs = data_batches.iter().collect();
    write_parquet_multi(
        &table_path,
        &data_refs,
        Some(hdfs_config),
        batch_size, // batch_size
    )
        .await
        .map_err(|e| {
            Box::new(std::io::Error::new(std::io::ErrorKind::Other, format!("写入parquet文件失败: {}", e)))
                as Box<dyn Error + Send + Sync>
        })?;
    log::info!("成功写入parquet文件到路径: {}", table_path);

    Ok(())
}


pub async fn init_clickhouse_stream_writer<T: Sync + Send + Serialize + Row + 'static>(
    handler: &(impl SinkHandler + Send + Sync),
    ck_config: CkConfig,
    max_concurrent_flushes: usize,
    batch_size: usize,
) -> Result<(AsyncCkSender<T>, JoinHandle<()>), Box<dyn Error + Send + Sync>> {

    let db_table_name = format!("{}.{}", handler.db_name(), handler.table_name());

    let optimal_buffer_size = std::cmp::max(batch_size * 4, 20000);

    let stream_config = StreamConfig::default()
        .with_buffer_size(optimal_buffer_size)
        .with_batch_size(batch_size)
        .with_flush_interval(Duration::from_millis(500))
        .with_max_retries(3)
        .with_backpressure_timeout(Duration::from_secs(600))
        .with_parallel_flush(true)
        .with_max_concurrent_flushes(max_concurrent_flushes);

    // 创建流式通道
    let (sender, receiver) = AsyncCkChannel::new::<T>(stream_config.clone(), 10);

    let ck_provider = CkProviderImpl::new(ck_config);

    // 创建流处理器
    let processor = CkStreamProcessorBuilder::new()
        .with_receiver(receiver)
        .with_provider(ck_provider.clone())
        .with_config(stream_config)
        .with_table_name(db_table_name)
        .build()?;

    // 启动流处理器任务
    let processor_handle = tokio::spawn(async move {
        if let Err(e) = processor.start().await {
            eprintln!("流处理器错误: {:?}", e);
        }
    });

    // processor_handle.await?;
    Ok((sender, processor_handle))
}
