use sysinfo::{System, Pid};
use std::time::Duration;
use tokio::time::sleep;

/// 内存监控器
pub struct MemoryMonitor {
    system: System,
    threshold_gb: f64,
}

impl MemoryMonitor {
    /// 创建新的内存监控器
    pub fn new(threshold_gb: f64) -> Self {
        let mut system = System::new_all();
        system.refresh_all();

        Self {
            system,
            threshold_gb,
        }
    }

    /// 获取当前进程的内存使用量（单位：GB）
    pub fn get_current_memory_usage_gb(&mut self) -> f64 {
        self.system.refresh_processes_specifics(
            sysinfo::ProcessesToUpdate::All,
            true,
            sysinfo::ProcessRefreshKind::everything()
        );

        let current_pid = Pid::from(std::process::id() as usize);

        if let Some(process) = self.system.process(current_pid) {
            let memory_bytes = process.memory();
            // 转换为GB
            memory_bytes as f64 / (1024.0 * 1024.0 * 1024.0)
        } else {
            0.0
        }
    }

    /// 检查内存使用量是否超过阈值
    pub fn is_memory_over_threshold(&mut self) -> bool {
        let current_memory = self.get_current_memory_usage_gb();
        current_memory > self.threshold_gb
    }

    /// 等待内存使用量降到阈值以下
    /// 返回是否成功（如果超时则返回false）
    pub async fn wait_for_memory_below_threshold(&mut self, max_retries: u32) -> bool {
        for retry_count in 0..max_retries {
            let current_memory = self.get_current_memory_usage_gb();

            if current_memory <= self.threshold_gb {
                if retry_count > 0 {
                    log::info!("内存使用量已降到阈值以下: {:.2}GB <= {:.2}GB，继续执行任务",
                              current_memory, self.threshold_gb);
                }
                return true;
            }

            log::warn!("当前内存使用量: {:.2}GB 超过阈值: {:.2}GB，等待1秒后重试 (第{}/{}次)",
                      current_memory, self.threshold_gb, retry_count + 1, max_retries);

            // 等待1秒
            sleep(Duration::from_secs(1)).await;
        }

        let final_memory = self.get_current_memory_usage_gb();
        log::error!("内存使用量持续超过阈值: {:.2}GB > {:.2}GB，已重试{}次，放弃等待",
                   final_memory, self.threshold_gb, max_retries);
        false
    }

    /// 检查并等待内存条件满足
    /// 如果内存超过阈值，会等待直到降到阈值以下或超时
    pub async fn check_and_wait_memory(&mut self) -> bool {
        let current_memory = self.get_current_memory_usage_gb();

        if current_memory <= self.threshold_gb {
            log::debug!("当前内存使用量: {:.2}GB，在阈值范围内: {:.2}GB",
                       current_memory, self.threshold_gb);
            return true;
        }

        log::warn!("当前内存使用量: {:.2}GB 超过阈值: {:.2}GB，开始等待内存释放",
                  current_memory, self.threshold_gb);

        // 最多重试30次（30秒）
        self.wait_for_memory_below_threshold(30).await
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_memory_monitor() {
        let mut monitor = MemoryMonitor::new(1.0); // 1GB阈值
        let memory_usage = monitor.get_current_memory_usage_gb();
        println!("当前内存使用量: {:.2}GB", memory_usage);
        assert!(memory_usage >= 0.0);
    }

    #[tokio::test]
    async fn test_memory_check() {
        let mut monitor = MemoryMonitor::new(100.0); // 100GB阈值（应该不会超过）
        let result = monitor.check_and_wait_memory().await;
        assert!(result);
    }
}
