use color_eyre::eyre::OptionExt;
use common::utils::stats::calculate_sum;
use dw_test_item::config::DwTestItemConfig;
use dw_test_item::task::cp_task_params::CpTaskParams;
use dw_test_item::task::cp_test_item_task::CpTestItemTask;
use dw_test_item::task::ft_task_params::FtTaskParams;
use dw_test_item::task::ft_test_item_task::FtTestItemTask;
use jni::objects::{JClass, JString};
use jni::sys::{jdoubleArray, jobject, jstring};
use jni::JNIEnv;
use jni_bridge::jni_bridge::JavaClasses;
use jni_bridge::{create_big_decimal, get_double_array, handle_unwinded_scope};

use color_eyre::Result;
use std::error::Error;

mod memory_monitor;
use memory_monitor::MemoryMonitor;

#[no_mangle]
pub extern "system" fn Java_com_guwave_onedata_dataware_dw_testItem_spark_jni_Native_00024_sum(
    mut env: JNIEnv,
    _class: JClass,
    values: jdoubleArray,
) -> jobject {
    if !JavaClasses::inited() {
        JavaClasses::init(&env);
    }
    handle_unwinded_scope(|| -> Result<jobject, Box<dyn Error + Send + Sync>> {
        let arr = get_double_array(&mut env, values).ok_or_eyre("Failed to get double array")?;
        let value = calculate_sum(&arr).ok_or_eyre("Failed to calculate sum")?;
        Ok(create_big_decimal(&mut env, value))
    })
}

#[no_mangle]
pub extern "system" fn Java_com_guwave_onedata_dataware_dw_testItem_spark_jni_Native_00024_cpTask(
    env: JNIEnv,
    _class: JClass,
    value: jstring,
) -> bool {
    if !JavaClasses::inited() {
        JavaClasses::init(&env);
    }
    unsafe {
        std::env::set_var("RUST_BACKTRACE", "1");
    }

    handle_unwinded_scope(|| -> Result<bool, Box<dyn Error + Send + Sync>> {
        let param = env.get_string(unsafe { JString::from_raw(value) }).map(String::from)?;

        // Create a new Tokio runtime for executing async code
        let rt = tokio::runtime::Builder::new_multi_thread()
            .worker_threads(8)
            .enable_all()
            .build()?;

        rt.block_on(async {
            // 获取配置
            let config = DwTestItemConfig::get_config().map_err(|e| color_eyre::eyre::eyre!("获取配置失败: {}", e))?;

            // 创建内存监控器
            let mut memory_monitor = MemoryMonitor::new(config.memory_threshold_gb);

            // 检查内存使用量，如果超过阈值则等待
            if !memory_monitor.check_and_wait_memory().await {
                return Err(color_eyre::eyre::eyre!("内存使用量持续超过阈值，任务执行被取消"));
            }

            // 执行任务
            let task = CpTestItemTask::new();
            task.do_task(CpTaskParams::new(&param)?)
                .await
                .map_err(|e| color_eyre::eyre::eyre!("任务执行失败: {}", e))?;
            Ok(())
        })?;

        Ok(true)
    })
}

#[no_mangle]
pub extern "system" fn Java_com_guwave_onedata_dataware_dw_testItem_spark_jni_Native_00024_ftTask(
    env: JNIEnv,
    _class: JClass,
    value: jstring,
) -> bool {
    if !JavaClasses::inited() {
        JavaClasses::init(&env);
    }
    unsafe {
        std::env::set_var("RUST_BACKTRACE", "1");
    }

    handle_unwinded_scope(|| -> Result<bool, Box<dyn Error + Send + Sync>> {
        let param = env.get_string(unsafe { JString::from_raw(value) }).map(String::from)?;

        // Create a new Tokio runtime for executing async code
        let rt = tokio::runtime::Builder::new_multi_thread()
            .worker_threads(8)
            .enable_all()
            .build()?;

        rt.block_on(async {
            // 获取配置
            let config = DwTestItemConfig::get_config().map_err(|e| color_eyre::eyre::eyre!("获取配置失败: {}", e))?;

            // 创建内存监控器
            let mut memory_monitor = MemoryMonitor::new(config.memory_threshold_gb);

            // 检查内存使用量，如果超过阈值则等待
            if !memory_monitor.check_and_wait_memory().await {
                return Err(color_eyre::eyre::eyre!("内存使用量持续超过阈值，任务执行被取消"));
            }

            // 执行任务
            let task = FtTestItemTask::new();
            task.do_task(FtTaskParams::new(&param)?)
                .await
                .map_err(|e| color_eyre::eyre::eyre!("任务执行失败: {}", e))?;
            Ok(())
        })?;

        Ok(true)
    })
}
